-- Whiteboard table setup for mydash project
-- This script creates the whiteboards table if it doesn't exist

CREATE TABLE IF NOT EXISTS `whiteboards` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `image_data` longtext NOT NULL,
    `width` int(11) NOT NULL,
    `height` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert some sample whiteboards for testing
INSERT IGNORE INTO `whiteboards` (`name`, `image_data`, `width`, `height`) VALUES
('Sample Whiteboard 1', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 800, 600),
('Test Board', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 1024, 768);
